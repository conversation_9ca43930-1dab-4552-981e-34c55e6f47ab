'use client';

import type { UseChatHelpers } from '@ai-sdk/react';
import { DragDropContext, Droppable, type DropResult } from '@hello-pangea/dnd';
import { Typography } from '@mui/joy';
import type { SxProps } from '@mui/joy/styles/types';
import { AnimatePresence, motion } from 'framer-motion';
import React, { useRef, memo, useState } from 'react';
import { useLocale } from '@bika/contents/i18n/context';
import type { AIChatOption, AIChatContextVO, AIChatModelOption } from '@bika/types/ai/vo';
import { useGlobalContext } from '@bika/types/website/context';
import { Button, IconButton } from '@bika/ui/button';
import { useAttachmentUpload } from '@bika/ui/components/image-crop-upload/index';
import { Dropdown, Menu, MenuButton, MenuItem } from '@bika/ui/dropdown';
import { Textarea } from '@bika/ui/form-components';
import AddOutlined from '@bika/ui/icons/components/add_outlined';
import ArrowDownOutlined from '@bika/ui/icons/components/arrow_down_outlined';
import AttachmentOutlined from '@bika/ui/icons/components/attachment_outlined';
import PauseOutlined from '@bika/ui/icons/components/pause_outlined';
import SendOutlined from '@bika/ui/icons/doc_hide_components/send_outlined';
import { Box, Stack } from '@bika/ui/layouts';
import { SelectInput } from '@bika/ui/shared/types-form/select-input';
import { VoiceInput, type VoiceInputHandle } from '@bika/ui/voice-input';
import { AIChatContextRenderer } from './ai-chat-context';
import { useInputStore } from './input-store';
import { useChatScrollToBottom } from './use-chat-scroll-to-bottom';
import { AttachmentPreview, type UploadingAttachment } from './utils/attachment-preview';
import { file2Base64 } from './utils/utils';

export interface AIChatInputOptions {
  // context弹出菜单，即+号
  // contextMenu?: { label: string | React.ReactNode; onClick: () => void }[];

  allowContextMenu?: string[];

  // 有什么 context
  context?: AIChatContextVO[];

  // 自定义选项
  options?: AIChatOption[];

  // 模型选项
  modelOptions?: AIChatModelOption[];
  sx?: SxProps;

  // 技能集图标
  skillsetIcons?: React.ReactNode;

  // 文件上传接受的文件类型
  uploadAccept?: string;
}

type AIChatInputProps = {
  input: string;
  setInput: (input: string) => void;
  onChange(e: React.ChangeEvent<HTMLTextAreaElement>): void;

  disabled: boolean;
  placeholder?: string;
  handleSubmit: (event: { preventDefault?: () => void }) => void;
  isAtBottom?: boolean;
  status?: UseChatHelpers['status'];
  stop?: () => void;
} & AIChatInputOptions;

export interface AIChatInputRefHandle {
  textarea: HTMLTextAreaElement;
  option: string | undefined;
}

function AIChatInputInternal(props: AIChatInputProps, ref: React.Ref<AIChatInputRefHandle>) {
  const [option, setOption] = useState<string | undefined>(() => props.options?.[0]?.value);
  const {
    input,
    disabled,
    setInput,
    handleSubmit,
    sx,
    isAtBottom = true,
    status,
    stop,
    skillsetIcons,
    uploadAccept = 'image/png, image/jpeg, image/gif, image/webp, application/pdf',
  } = props;
  const isNeedStop = status === 'streaming' || status === 'submitted';
  const textareaRef = React.useRef<HTMLTextAreaElement>(null);

  const [focus, setFocus] = useState(false);
  const voiceInputRef = useRef<VoiceInputHandle>(null);
  const [isComposing, setIsComposing] = useState(false);
  const globalContext = useGlobalContext();

  // Drag and drop state management
  const [isDragOver, setIsDragOver] = useState(false);
  const [dragCounter, setDragCounter] = useState(0);

  const { scrollToBottom } = useChatScrollToBottom();

  const { upload: doUploadFile } = useAttachmentUpload();

  const { attachments, setAttachments } = useInputStore();
  const [uploadingAttachments, setUploadingAttachments] = useState<UploadingAttachment[]>([]);

  const uploadFiles = async (files: FileList | null) => {
    if (!files) return;
    const curUploadingAttachments: UploadingAttachment[] = [];
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const base64 = await file2Base64(file);
      curUploadingAttachments.push({
        id: i,
        name: file.name,
        base64,
        contentType: file.type,
      });
    }
    setUploadingAttachments(curUploadingAttachments);

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const rlt = await doUploadFile({
        file,
        filePrefix: 'ai',
      });
      setUploadingAttachments((pre) => pre.filter((item) => item.id !== i));

      const filePublicUrl = globalContext.servers.storagePublicUrl ?? '';
      const fileUrl = `${filePublicUrl}/${rlt.path}`;
      setAttachments((prevAttachments) => [
        ...prevAttachments,
        {
          id: rlt.id,
          name: file.name,
          contentType: file.type,
          url: fileUrl,
        },
      ]);
    }
  };

  // Drag and drop event handlers
  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragCounter((prev) => prev + 1);
    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragCounter((prev) => {
      const newCounter = prev - 1;
      if (newCounter === 0) {
        setIsDragOver(false);
      }
      return newCounter;
    });
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
    setDragCounter(0);

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      // Filter files based on uploadAccept prop
      const acceptedTypes = uploadAccept.split(',').map((type) => type.trim());
      const validFiles: File[] = [];

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const isValidType = acceptedTypes.some((acceptType) => {
          if (acceptType.startsWith('.')) {
            // File extension check
            return file.name.toLowerCase().endsWith(acceptType.toLowerCase());
          }
          // MIME type check
          return file.type.match(acceptType.replace('*', '.*'));
        });

        if (isValidType) {
          validFiles.push(file);
        }
      }

      if (validFiles.length > 0) {
        // Convert to FileList-like object
        const dataTransfer = new DataTransfer();
        validFiles.forEach((file) => dataTransfer.items.add(file));
        uploadFiles(dataTransfer.files);
      }
    }
  };

  // Handle drag and drop from @hello-pangea/dnd (for internal reordering)
  const handleDragEnd = (result: DropResult) => {
    // This is for internal drag and drop reordering, not file uploads
    // We don't need to implement anything here for file uploads
    console.log('Internal drag end:', result);
  };

  // Keyboard support for file upload
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Ctrl/Cmd + U for file upload
    if ((e.ctrlKey || e.metaKey) && e.key === 'u') {
      e.preventDefault();
      const inputEle = document.createElement('input');
      inputEle.type = 'file';
      inputEle.multiple = true;
      inputEle.accept = uploadAccept;
      inputEle.onchange = (event) => {
        const files = (event.target as HTMLInputElement).files;
        uploadFiles(files);
      };
      inputEle.click();
    }
  };

  React.useImperativeHandle(ref, () => ({
    textarea: textareaRef.current!,
    option,
  }));

  const locale = useLocale();
  const { t, lang } = locale;

  return (
    <DragDropContext onDragEnd={handleDragEnd}>
      <Droppable droppableId="ai-chat-input-drop-zone">
        {(provided, _snapshot) => (
          <Box
            ref={provided.innerRef}
            {...provided.droppableProps}
            className="flex flex-col shrink-0 bottom-0"
            sx={{
              width: '100%',
              maxWidth: '720px',
              // padding: '16px 0 24px 0',
              padding: '16px 16px',
              borderRadius: '10px',
              position: 'relative',
              border: (() => {
                if (isDragOver) return '1px dashed var(--brand)';
                if (focus) return '1px solid var(--brand)';
                return '1px solid var(--border-default)';
              })(),
              // transition: 'all 0.2s ease-in-out',
              ...(isDragOver && {
                background: 'color-mix(in srgb, var(--brand) 10%, var(--bg-elevated) 30%)',
                // boxShadow: '0 0 0 4px color-mix(in srgb, var(--brand) 20%, transparent)',
              }),
              '&:hover': {
                cursor: 'text',
                background: isDragOver
                  ? 'color-mix(in srgb, var(--brand) 15%, var(--bg-elevated) 30%)'
                  : 'color-mix(in srgb, var(--bg-blur) 100%, var(--bg-elevated) 20%)',
              },
              ...sx,
            }}
            onClick={() => {
              if (textareaRef.current) {
                textareaRef.current.focus();
              }
            }}
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            onKeyDown={handleKeyDown}
            role="region"
            aria-label="Chat input with file drop zone"
            aria-describedby={isDragOver ? 'drag-drop-instructions' : undefined}
            tabIndex={-1}
          >
            <AnimatePresence>
              {!isAtBottom && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 10 }}
                  transition={{ type: 'spring', stiffness: 300, damping: 20 }}
                  className="absolute left-1/2 top-[-32px] -translate-x-1/2 z-50"
                >
                  <IconButton
                    onClick={(event) => {
                      event.preventDefault();
                      event.stopPropagation();
                      scrollToBottom();
                    }}
                    variant="solid"
                    sx={{
                      borderRadius: '50%',
                      background: 'var(--bg-surface)',
                      border: '1px solid var(--border-default)',
                      boxShadow: 'var(--shadow-default)',
                      '&:hover': {
                        background: 'var(--bg-popup)',
                        boxShadow: 'var(--shadow-high)',
                      },
                    }}
                  >
                    <ArrowDownOutlined color={'var(--text-primary)'} />
                  </IconButton>
                </motion.div>
              )}
            </AnimatePresence>
            <Box
              sx={{
                // background: 'color-mix(in srgb, var(--bg-blur) 100%, var(--bg-elevated) 30%)',
                // padding: '4px 16px 12px 16px',
                borderRadius: '10px',
                // border: (() => {
                //   if (isDragOver) return '2px dashed var(--brand)';
                //   if (focus) return '1px solid var(--brand)';
                //   return '1px solid var(--border-default)';
                // })(),
                // transition: 'all 0.2s ease-in-out',
                // ...(isDragOver && {
                //   background: 'color-mix(in srgb, var(--brand) 10%, var(--bg-elevated) 30%)',
                //   boxShadow: '0 0 0 4px color-mix(in srgb, var(--brand) 20%, transparent)',
                // }),
                // '&:hover': {
                //   cursor: 'text',
                //   background: isDragOver
                //     ? 'color-mix(in srgb, var(--brand) 15%, var(--bg-elevated) 30%)'
                //     : 'color-mix(in srgb, var(--bg-blur) 100%, var(--bg-elevated) 20%)',
                // },
              }}
            >
              {/* Drag overlay */}
              {isDragOver && (
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    // background: 'color-mix(in srgb, var(--brand) 5%, transparent)',
                    borderRadius: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    zIndex: 10,
                    pointerEvents: 'none',
                  }}
                >
                  <Typography
                    id="drag-drop-instructions"
                    level="b4"
                    textColor="var(--text-primary)"
                    sx={
                      {
                        // background: 'var(--bg-surface)',
                        // border: '2px dashed var(--brand)',
                        // borderRadius: '8px',
                        // padding: '16px 24px',
                        // display: 'flex',
                        // alignItems: 'center',
                        // gap: '8px',
                        // color: 'var(--brand)',
                        // fontSize: '16px',
                        // fontWeight: 500,
                        // boxShadow: 'var(--shadow-high)',
                      }
                    }
                    role="status"
                    aria-live="polite"
                  >
                    {t.tips.drop_files_here}
                  </Typography>
                </Box>
              )}

              <div>
                <div className="flex-1">
                  <div className="mb-[8px] flex items-center gap-1">
                    <div className="flex items-center gap-1 ml-2">
                      {/* Contexts */}
                      {props.context?.map((item, index) => <AIChatContextRenderer key={index} value={item} />)}

                      {/* Attachments */}
                      {attachments.map((item) => (
                        <AttachmentPreview
                          key={item.id}
                          data={{
                            type: 'preview',
                            attachment: item,
                          }}
                        />
                      ))}
                      {uploadingAttachments.length > 0 &&
                        uploadingAttachments.map((item) => (
                          <AttachmentPreview
                            key={item.id}
                            data={{
                              type: 'uploading',
                              attachment: item,
                            }}
                          />
                        ))}
                    </div>
                  </div>
                  <Textarea
                    slotProps={{ textarea: { ref: textareaRef } }}
                    sx={{
                      width: '100%',
                      background: 'transparent',
                      boxShadow: 'none',
                      '--Textarea-focusedHighlight': 'transparent !important',
                      fontSize: '16px',
                      lineHeight: '24px',
                      padding: 0,
                      // height: `${Math.min(input.split('\n').length * 20 || 20, 60)}px`,
                      resize: 'none',
                      borderRadius: '8px',
                      '&.Mui-disabled': {
                        border: 0,
                      },
                    }}
                    minRows={2}
                    maxRows={12}
                    onFocus={() => setFocus(true)}
                    onBlur={() => setFocus(false)}
                    onCompositionStart={() => setIsComposing(true)}
                    onCompositionEnd={() => setIsComposing(false)}
                    disabled={disabled}
                    placeholder={props.placeholder || t.ai.type_message} // t.ai.type_message}
                    value={input}
                    onKeyDown={(e) => {
                      voiceInputRef.current?.stopListening();
                      if (e.key === 'Enter' && !e.shiftKey && !isComposing && !isNeedStop) {
                        handleSubmit(e);
                      }
                    }}
                    onChange={props.onChange}
                    onPaste={(e) => {
                      const clipboardData = e.clipboardData;
                      if (clipboardData?.files) {
                        const files = Array.from(clipboardData.files).filter((file) => file.type.startsWith('image/'));
                        if (files.length > 0) {
                          e.preventDefault();
                          const fileList = new DataTransfer();
                          for (const file of files) {
                            fileList.items.add(file);
                          }
                          uploadFiles(fileList.files);
                        }
                      }
                    }}
                  />
                </div>
                <div className="flex flex-row justify-between items-end">
                  <Stack display={'flex'} direction={'row'} gap={1} alignItems={'end'}>
                    {props.options && option && (
                      <SelectInput
                        hideSearch
                        label=""
                        disabled={props.options.filter((_option) => !_option.disabled).length < 2}
                        value={option}
                        onChange={(newOpt) => {
                          if (newOpt) setOption?.(newOpt);
                        }}
                        options={props.options}
                        classes={{
                          joySelect: {
                            height: '32px',
                            minHeight: '32px',
                          },
                        }}
                        sx={{
                          width: '100px',
                          height: '32px',
                          '& > .MuiSelect-select': {
                            padding: '6px 14px',
                          },
                        }}
                      />
                    )}
                    {props.allowContextMenu?.length && (
                      <Dropdown>
                        <MenuButton
                          sx={{
                            width: '32px',
                            height: '32px',
                            padding: '4px',
                            background: 'var(--bg-controls)',
                            border: '1px solid var(--border-default)',
                            '&:hover': {
                              background: 'var(--bg-controls-hover)',
                            },
                          }}
                        >
                          {' '}
                          <AddOutlined color="var(--text-secondary)" />
                        </MenuButton>
                        <Menu>
                          <MenuItem
                            onClick={() => {
                              const inputEle = document.createElement('input');
                              inputEle.type = 'file';
                              inputEle.multiple = true;
                              inputEle.accept = uploadAccept;
                              inputEle.onchange = (e) => {
                                const files = (e.target as HTMLInputElement).files;
                                uploadFiles(files);
                              };
                              inputEle.click();
                            }}
                          >
                            <AttachmentOutlined />
                            <div>{t.global.copilot.upload_file}</div>
                          </MenuItem>
                        </Menu>
                      </Dropdown>
                    )}
                    {skillsetIcons}
                  </Stack>

                  <Box
                    className="flex items-center space-x-[8px]"
                    sx={{
                      ml: 'auto',
                    }}
                  >
                    <VoiceInput
                      ref={voiceInputRef}
                      locale={lang}
                      onChange={(text) => {
                        setInput(text);
                      }}
                    />

                    <Button
                      // loading={status === 'streaming'}
                      disabled={!isNeedStop && (disabled || (!input.trim() && attachments.length === 0))}
                      onClick={(e) => {
                        voiceInputRef.current?.stopListening();
                        if (isNeedStop) {
                          stop?.();
                        } else {
                          handleSubmit(e);
                        }
                      }}
                      sx={{
                        marginLeft: '8px',
                        '&.Mui-disabled': {
                          backgroundColor: 'var(--brand) !important',
                          opacity: 0.5,
                        },
                      }}
                      color={isNeedStop ? 'danger' : 'primary'}
                      startDecorator={isNeedStop ? <PauseOutlined currentColor /> : <SendOutlined currentColor />}
                    >
                      {isNeedStop ? 'stop' : t.action.send}
                    </Button>
                  </Box>
                </div>
              </div>
            </Box>
            {provided.placeholder}
          </Box>
        )}
      </Droppable>
    </DragDropContext>
  );
}

export const AIChatInput = memo(
  React.forwardRef(AIChatInputInternal),
  (prevProps, nextProps) =>
    prevProps.input === nextProps.input &&
    prevProps.disabled === nextProps.disabled &&
    prevProps.isAtBottom === nextProps.isAtBottom &&
    prevProps.status === nextProps.status,
);
